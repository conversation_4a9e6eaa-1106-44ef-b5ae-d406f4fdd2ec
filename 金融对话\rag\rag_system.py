"""
RAG检索系统
负责文档嵌入、相似度搜索和知识检索功能
"""
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Optional, Tuple
from loguru import logger
import numpy as np
import sys
from pathlib import Path
import gc
import psutil
import os
import re
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue
import multiprocessing as mp

# 添加父目录到Python路径
parent_dir = Path(__file__).parent.parent
sys.path.insert(0, str(parent_dir))

from rag.milvus_manager import MilvusManager
from idconfig.config import Config

class RAGSystem:
    def __init__(self):
        self.config = Config()
        self.embedding_model = None
        self.milvus_manager = MilvusManager()

        # 文本截断配置 (BGE-M3模型配置)
        self.max_seq_length = 8192  # BGE-M3最大序列长度
        self.safe_max_length = 7500  # 安全的最大长度，留出一些余量
        self.overlap_ratio = 0.1  # 重叠比例，用于长文本分块

        # 批处理配置
        self.default_batch_size = 32  # 默认批处理大小
        self.max_batch_size = 64  # 最大批处理大小
        self.min_batch_size = 8   # 最小批处理大小
        self.adaptive_batch_size = True  # 是否启用自适应批处理大小

        # 性能监控
        self.encoding_times = []  # 记录编码时间
        self.batch_stats = {
            'total_batches': 0,
            'total_texts': 0,
            'total_time': 0,
            'avg_batch_time': 0,
            'optimal_batch_size': self.default_batch_size
        }

        # 线程安全
        self._lock = threading.Lock()
        
    def initialize(self):
        """初始化RAG系统"""
        try:
            # 初始化嵌入模型
            logger.info(f"加载嵌入模型: {self.config.EMBEDDING_MODEL}")
            self.embedding_model = SentenceTransformer(self.config.EMBEDDING_MODEL)
            logger.info("嵌入模型加载完成")

            # 验证模型配置
            if not self._validate_model_config():
                return False

            # 初始化Milvus管理器
            if not self.milvus_manager.initialize():
                logger.error("Milvus管理器初始化失败")
                return False

            logger.info("RAG系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"RAG系统初始化失败: {e}")
            return False

    def _validate_model_config(self):
        """验证模型配置"""
        try:
            # 测试编码一个简单文本
            test_text = "测试文本"
            test_embedding = self.embedding_model.encode(test_text, normalize_embeddings=True)

            actual_dim = len(test_embedding)
            expected_dim = self.config.VECTOR_DIM

            logger.info(f"模型实际维度: {actual_dim}, 配置维度: {expected_dim}")

            # 检查是否为BGE-M3模型
            if "bge-m3" in self.config.EMBEDDING_MODEL.lower():
                if actual_dim != 1024:
                    logger.error(f"BGE-M3模型应该是1024维，但检测到{actual_dim}维")
                    return False
                if expected_dim != 1024:
                    logger.error("BGE-M3模型需要设置VECTOR_DIM=1024")
                    return False
                logger.info("✓ BGE-M3模型配置验证通过")

            # 检查向量是否归一化
            norm = np.linalg.norm(test_embedding)
            if abs(norm - 1.0) > 0.01:
                logger.warning(f"向量未完全归一化，norm={norm:.4f}")
            else:
                logger.info("✓ 向量归一化验证通过")

            return True

        except Exception as e:
            logger.error(f"模型配置验证失败: {e}")
            return False

    def _estimate_token_count(self, text: str) -> int:
        """估算文本的token数量（简单估算）"""
        # 对于中文文本，大约1个字符对应1个token
        # 对于英文文本，大约4个字符对应1个token
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(text) - chinese_chars

        estimated_tokens = chinese_chars + (english_chars // 4)
        return estimated_tokens

    def _truncate_text_smart(self, text: str, max_length: Optional[int] = None) -> str:
        """智能截断文本，保持语义完整性"""
        if max_length is None:
            max_length = self.safe_max_length

        # 估算token数量
        estimated_tokens = self._estimate_token_count(text)

        if estimated_tokens <= max_length:
            return text

        logger.info(f"文本过长({estimated_tokens} tokens)，开始智能截断到{max_length} tokens")

        # 计算截断比例
        truncate_ratio = max_length / estimated_tokens
        target_char_length = int(len(text) * truncate_ratio * 0.9)  # 留10%余量

        # 尝试在句子边界截断
        sentences = re.split(r'[。！？；\n]', text)
        truncated_text = ""
        current_length = 0

        for sentence in sentences:
            sentence_with_punct = sentence + "。" if sentence.strip() else ""
            if current_length + len(sentence_with_punct) <= target_char_length:
                truncated_text += sentence_with_punct
                current_length += len(sentence_with_punct)
            else:
                break

        # 如果按句子截断后文本太短，则按字符截断
        if len(truncated_text) < target_char_length * 0.5:
            truncated_text = text[:target_char_length]
            # 尝试在词边界截断
            if len(truncated_text) < len(text):
                last_space = truncated_text.rfind(' ')
                last_chinese = max(truncated_text.rfind('，'), truncated_text.rfind('。'))
                cut_point = max(last_space, last_chinese)
                if cut_point > target_char_length * 0.8:
                    truncated_text = truncated_text[:cut_point]

        final_tokens = self._estimate_token_count(truncated_text)
        logger.info(f"文本截断完成: {len(text)} -> {len(truncated_text)} 字符, 约{final_tokens} tokens")

        return truncated_text

    def _split_long_text(self, text: str, max_length: Optional[int] = None) -> List[str]:
        """将长文本分割成多个块，带重叠"""
        if max_length is None:
            max_length = self.safe_max_length

        estimated_tokens = self._estimate_token_count(text)

        if estimated_tokens <= max_length:
            return [text]

        logger.info(f"长文本分块: {estimated_tokens} tokens -> 多个{max_length} token块")

        # 计算分块参数
        overlap_length = int(max_length * self.overlap_ratio)
        chunk_length = max_length - overlap_length

        # 按字符长度估算分块
        char_per_token = len(text) / estimated_tokens
        chunk_char_length = int(chunk_length * char_per_token)
        overlap_char_length = int(overlap_length * char_per_token)

        chunks = []
        start = 0

        while start < len(text):
            end = min(start + chunk_char_length, len(text))

            # 尝试在句子边界分割
            if end < len(text):
                # 寻找最近的句子结束符
                sentence_end = -1
                for punct in ['。', '！', '？', '；', '\n']:
                    pos = text.rfind(punct, start, end)
                    if pos > sentence_end:
                        sentence_end = pos

                if sentence_end > start + chunk_char_length * 0.7:
                    end = sentence_end + 1

            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)

            # 计算下一个块的起始位置（带重叠）
            if end >= len(text):
                break
            start = max(start + chunk_char_length - overlap_char_length, end - overlap_char_length)

        logger.info(f"文本分块完成: 生成{len(chunks)}个块")
        return chunks

    def _calculate_optimal_batch_size(self, text_lengths: List[int]) -> int:
        """根据文本长度和系统资源计算最优批处理大小"""
        if not self.adaptive_batch_size:
            return self.default_batch_size

        # 获取系统内存信息
        memory = psutil.virtual_memory()
        available_memory_gb = memory.available / (1024**3)

        # 计算平均文本长度
        avg_length = sum(text_lengths) / len(text_lengths) if text_lengths else 1000

        # 根据文本长度和可用内存调整批处理大小
        if avg_length > 4000:  # 长文本
            base_batch_size = max(self.min_batch_size, int(self.default_batch_size * 0.5))
        elif avg_length > 2000:  # 中等文本
            base_batch_size = self.default_batch_size
        else:  # 短文本
            base_batch_size = min(self.max_batch_size, int(self.default_batch_size * 1.5))

        # 根据可用内存调整
        if available_memory_gb < 4:
            base_batch_size = max(self.min_batch_size, int(base_batch_size * 0.5))
        elif available_memory_gb > 8:
            base_batch_size = min(self.max_batch_size, int(base_batch_size * 1.2))

        # 根据历史性能调整
        if self.batch_stats['total_batches'] > 10:
            if self.batch_stats['avg_batch_time'] > 10:  # 如果平均批处理时间过长
                base_batch_size = max(self.min_batch_size, int(base_batch_size * 0.8))
            elif self.batch_stats['avg_batch_time'] < 3:  # 如果处理很快
                base_batch_size = min(self.max_batch_size, int(base_batch_size * 1.1))

        return max(self.min_batch_size, min(self.max_batch_size, base_batch_size))

    def _update_batch_stats(self, batch_size: int, batch_time: float, num_texts: int):
        """更新批处理统计信息"""
        with self._lock:
            self.batch_stats['total_batches'] += 1
            self.batch_stats['total_texts'] += num_texts
            self.batch_stats['total_time'] += batch_time
            self.batch_stats['avg_batch_time'] = self.batch_stats['total_time'] / self.batch_stats['total_batches']

            # 更新最优批处理大小
            if batch_time < self.batch_stats['avg_batch_time'] * 0.9:
                self.batch_stats['optimal_batch_size'] = min(self.max_batch_size, batch_size + 2)
            elif batch_time > self.batch_stats['avg_batch_time'] * 1.2:
                self.batch_stats['optimal_batch_size'] = max(self.min_batch_size, batch_size - 2)

    def encode_texts_batch(self, texts: List[str], show_progress: bool = True) -> List[List[float]]:
        """批量编码文本，支持自适应批处理和文本截断"""
        if not texts:
            return []

        if not self.embedding_model:
            logger.error("嵌入模型未初始化")
            return []

        # 预处理文本：截断过长文本
        processed_texts = []
        for text in texts:
            truncated_text = self._truncate_text_smart(text)
            processed_texts.append(truncated_text)

        # 计算最优批处理大小
        text_lengths = [self._estimate_token_count(text) for text in processed_texts]
        optimal_batch_size = self._calculate_optimal_batch_size(text_lengths)

        logger.info(f"开始批量编码 {len(processed_texts)} 个文本，批处理大小: {optimal_batch_size}")

        all_embeddings = []
        total_batches = (len(processed_texts) + optimal_batch_size - 1) // optimal_batch_size

        for i in range(0, len(processed_texts), optimal_batch_size):
            batch_start_time = time.time()
            batch_texts = processed_texts[i:i + optimal_batch_size]
            batch_num = i // optimal_batch_size + 1

            if show_progress:
                logger.info(f"处理批次 {batch_num}/{total_batches} ({len(batch_texts)} 个文本)")

            try:
                # 批量编码
                batch_embeddings = self.embedding_model.encode(
                    batch_texts,
                    normalize_embeddings=True,
                    show_progress_bar=False,
                    batch_size=len(batch_texts)  # 确保一次性处理整个批次
                )

                # 转换为列表格式
                for embedding in batch_embeddings:
                    if isinstance(embedding, np.ndarray):
                        all_embeddings.append(embedding.tolist())
                    else:
                        all_embeddings.append(list(embedding))

                # 更新统计信息
                batch_time = time.time() - batch_start_time
                self._update_batch_stats(len(batch_texts), batch_time, len(batch_texts))

                if show_progress and batch_time > 5:
                    logger.info(f"批次 {batch_num} 完成，耗时: {batch_time:.2f}秒")

                # 内存管理
                if batch_num % 5 == 0:  # 每5个批次检查一次内存
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > 85:
                        logger.warning(f"内存使用率过高: {memory_percent:.1f}%，执行垃圾回收")
                        gc.collect()

            except Exception as e:
                logger.error(f"批次 {batch_num} 编码失败: {e}")
                # 为失败的批次添加空向量
                for _ in batch_texts:
                    all_embeddings.append([])

        success_count = sum(1 for emb in all_embeddings if emb)
        logger.info(f"批量编码完成: {success_count}/{len(processed_texts)} 成功")

        return all_embeddings
    
    def encode_text(self, text: str, auto_truncate: bool = True) -> List[float]:
        """将文本编码为向量，支持自动截断"""
        try:
            if not self.embedding_model:
                logger.error("嵌入模型未初始化")
                return []

            # 自动截断过长文本
            processed_text = text
            if auto_truncate:
                processed_text = self._truncate_text_smart(text)

            start_time = time.time()

            # 直接编码（移除超时机制以简化代码）
            embedding = self.embedding_model.encode(processed_text, normalize_embeddings=True)

            encode_time = time.time() - start_time
            if encode_time > 5:  # 如果编码时间超过5秒，记录警告
                logger.warning(f"向量编码耗时较长: {encode_time:.2f}秒，文本长度: {len(processed_text)}")

            # 检查向量维度
            actual_dim = len(embedding)
            expected_dim = self.config.VECTOR_DIM

            if actual_dim != expected_dim:
                logger.warning(f"向量维度不匹配: {actual_dim} != {expected_dim}")
                # BGE-M3应该是1024维，如果不匹配说明配置有问题
                if actual_dim == 1024 and expected_dim != 1024:
                    logger.error("检测到BGE-M3模型(1024维)，但配置的VECTOR_DIM不是1024")
                    logger.error("请在config.py中设置 VECTOR_DIM = 1024")
                    return []

                # 如果维度不匹配，进行填充或截断（不推荐）
                if actual_dim < expected_dim:
                    embedding = np.pad(embedding, (0, expected_dim - actual_dim))
                    logger.warning(f"向量已填充到{expected_dim}维")
                else:
                    embedding = embedding[:expected_dim]
                    logger.warning(f"向量已截断到{expected_dim}维")

            # 确保向量是归一化的（BGE-M3推荐使用归一化向量）
            if not hasattr(embedding, 'dtype'):
                embedding = np.array(embedding, dtype=np.float32)

            # 检查向量是否有效
            if np.any(np.isnan(embedding)) or np.any(np.isinf(embedding)):
                logger.error("生成的向量包含无效值(NaN或Inf)")
                return []

            return embedding.tolist()

        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            return []
    
    def add_knowledge(self, content: str, category: str, source: str = ""):
        """添加知识到知识库"""
        try:
            # 生成嵌入向量
            embedding = self.encode_text(content)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "content": content,
                "category": category,
                "source": source,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_knowledge(data)
            
        except Exception as e:
            logger.error(f"添加知识失败: {e}")
            return False
    
    def add_conversation_history(self, session_id: str, user_query: str, 
                               assistant_response: str, timestamp: int):
        """添加对话历史"""
        try:
            # 将用户查询和助手回复组合作为嵌入内容
            combined_text = f"用户: {user_query}\n助手: {assistant_response}"
            
            # 生成嵌入向量
            embedding = self.encode_text(combined_text)
            if not embedding:
                logger.error("生成嵌入向量失败")
                return False
            
            # 准备数据
            data = [{
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "timestamp": timestamp,
                "embedding": embedding
            }]
            
            # 插入到Milvus
            return self.milvus_manager.insert_history(data)
            
        except Exception as e:
            logger.error(f"添加对话历史失败: {e}")
            return False
    
    def search_knowledge(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索知识库
            results = self.milvus_manager.search_knowledge(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"知识库搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索知识失败: {e}")
            return []
    
    def search_conversation_history(self, query: str, top_k: int = None) -> List[Dict[str, Any]]:
        """搜索相关对话历史"""
        try:
            # 生成查询向量
            query_embedding = self.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 搜索历史对话
            results = self.milvus_manager.search_history(query_embedding, top_k)
            
            # 过滤低相似度结果
            filtered_results = [
                result for result in results 
                if result["score"] >= self.config.SIMILARITY_THRESHOLD
            ]
            
            logger.info(f"历史对话搜索完成，找到 {len(filtered_results)} 条相关结果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"搜索对话历史失败: {e}")
            return []
    
    def retrieve_context(self, query: str, include_images: bool = True) -> Dict[str, Any]:
        """检索相关上下文信息"""
        try:
            # 搜索知识库
            knowledge_results = self.search_knowledge(query)

            # 搜索历史对话
            history_results = self.search_conversation_history(query)

            # 搜索相关图片（如果启用）
            image_results = []
            if include_images:
                image_results = self.search_related_images(query)

            # 组织上下文信息
            context = {
                "knowledge": knowledge_results,
                "history": history_results,
                "images": image_results,
                "query": query
            }

            logger.info(f"上下文检索完成 - 知识: {len(knowledge_results)}条, 历史: {len(history_results)}条, 图片: {len(image_results)}张")
            return context

        except Exception as e:
            logger.error(f"检索上下文失败: {e}")
            return {"knowledge": [], "history": [], "images": [], "query": query}
    
    def batch_add_knowledge(self, knowledge_list: List[Dict[str, str]], use_batch_encoding: bool = True):
        """批量添加知识，支持高效批处理编码"""
        try:
            logger.info(f"开始批量添加 {len(knowledge_list)} 个知识块到向量数据库")
            total_count = len(knowledge_list)

            if use_batch_encoding and total_count > 10:
                # 使用批量编码模式
                return self._batch_add_knowledge_optimized(knowledge_list)
            else:
                # 使用传统逐个编码模式（适合小批量）
                return self._batch_add_knowledge_traditional(knowledge_list)

        except Exception as e:
            logger.error(f"批量添加知识失败: {e}")
            return False

    def _batch_add_knowledge_optimized(self, knowledge_list: List[Dict[str, str]]) -> bool:
        """优化的批量添加知识方法"""
        try:
            total_count = len(knowledge_list)
            logger.info(f"使用优化批处理模式处理 {total_count} 个知识块")

            # 提取所有文本内容
            texts = [item["content"] for item in knowledge_list]

            # 批量编码所有文本
            logger.info("开始批量编码文本...")
            embeddings = self.encode_texts_batch(texts, show_progress=True)

            if len(embeddings) != len(knowledge_list):
                logger.error(f"编码结果数量不匹配: {len(embeddings)} != {len(knowledge_list)}")
                return False

            # 准备数据并分批插入数据库
            successful_count = 0
            db_batch_size = 100  # 数据库插入批次大小

            for i in range(0, total_count, db_batch_size):
                batch_data = []
                batch_end = min(i + db_batch_size, total_count)

                for j in range(i, batch_end):
                    if embeddings[j]:  # 只处理成功编码的项目
                        batch_data.append({
                            "content": knowledge_list[j]["content"],
                            "category": knowledge_list[j].get("category", "general"),
                            "source": knowledge_list[j].get("source", ""),
                            "embedding": embeddings[j]
                        })

                if batch_data:
                    logger.info(f"插入数据库批次 {i//db_batch_size + 1}: {len(batch_data)} 个知识块")
                    if self.milvus_manager.insert_knowledge(batch_data):
                        successful_count += len(batch_data)
                    else:
                        logger.error(f"数据库批次 {i//db_batch_size + 1} 插入失败")

                # 内存管理
                if (i // db_batch_size + 1) % 5 == 0:
                    memory_percent = psutil.virtual_memory().percent
                    if memory_percent > 80:
                        logger.warning(f"内存使用率: {memory_percent:.1f}%，执行垃圾回收")
                        gc.collect()

            logger.info(f"优化批处理完成: {successful_count}/{total_count} 个知识块成功添加")
            return successful_count > 0

        except Exception as e:
            logger.error(f"优化批量添加失败: {e}")
            return False

    def _batch_add_knowledge_traditional(self, knowledge_list: List[Dict[str, str]]) -> bool:
        """传统的批量添加知识方法（逐个编码）"""
        try:
            logger.info("使用传统模式逐个处理知识块")
            data = []
            total_count = len(knowledge_list)
            batch_size = 50  # 每批处理50个

            for i, item in enumerate(knowledge_list):
                try:
                    # 记录进度
                    if (i + 1) % 10 == 0 or i == 0:
                        logger.info(f"正在生成向量: {i + 1}/{total_count} ({((i + 1) / total_count * 100):.1f}%)")

                    embedding = self.encode_text(item["content"])
                    if embedding:
                        data.append({
                            "content": item["content"],
                            "category": item.get("category", "general"),
                            "source": item.get("source", ""),
                            "embedding": embedding
                        })
                    else:
                        logger.warning(f"跳过无法生成向量的知识块: {item.get('content', '')[:100]}...")

                    # 分批插入到数据库
                    if len(data) >= batch_size:
                        # 检查内存使用情况
                        memory_percent = psutil.virtual_memory().percent
                        if memory_percent > 80:
                            logger.warning(f"内存使用率较高: {memory_percent:.1f}%，执行垃圾回收")
                            gc.collect()

                        logger.info(f"插入批次数据到数据库: {len(data)} 个知识块 (内存使用: {memory_percent:.1f}%)")
                        if not self.milvus_manager.insert_knowledge(data):
                            logger.error(f"批次插入失败，已处理 {i + 1} 个知识块")
                            return False
                        data = []  # 清空已处理的数据
                        gc.collect()  # 强制垃圾回收

                except Exception as e:
                    logger.error(f"处理第 {i + 1} 个知识块时出错: {e}")
                    continue

            # 插入剩余的数据
            if data:
                logger.info(f"插入最后批次数据到数据库: {len(data)} 个知识块")
                if not self.milvus_manager.insert_knowledge(data):
                    logger.error("最后批次插入失败")
                    return False

            logger.info(f"传统批处理完成，共处理 {total_count} 个知识块")
            return True

        except Exception as e:
            logger.error(f"传统批量添加失败: {e}")
            return False

    def search_related_images(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """搜索与查询相关的图片"""
        try:
            # 导入多模态检索器
            from rag.multimodal_retrieval import MultimodalImageRetriever

            # 创建多模态检索器实例
            multimodal_retriever = MultimodalImageRetriever()
            if not multimodal_retriever.initialize():
                logger.warning("多模态检索器初始化失败，跳过图片搜索")
                return []

            # 搜索相关图片
            image_results = multimodal_retriever.search_images_by_text(query, top_k)

            # 过滤和格式化结果
            formatted_results = []
            for result in image_results:
                # 使用final_score作为主要评分标准
                score = result.get("final_score", result.get("similarity", 0))
                if score >= 0.2:  # 设置相似度阈值
                    formatted_result = {
                        "image_id": result.get("image_id"),
                        "pdf_name": result.get("pdf_name"),
                        "page_number": result.get("page_number"),
                        "image_index": result.get("image_index"),
                        "description": result.get("description"),
                        "image_type": result.get("image_type"),
                        "similarity": result.get("similarity", 0),
                        "final_score": score,
                        "width": result.get("width"),
                        "height": result.get("height")
                    }
                    formatted_results.append(formatted_result)

            logger.info(f"图片搜索完成，找到 {len(formatted_results)} 张相关图片")
            return formatted_results

        except ImportError:
            logger.warning("多模态功能不可用，跳过图片搜索")
            return []
        except Exception as e:
            logger.error(f"搜索相关图片失败: {e}")
            return []

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            "batch_stats": self.batch_stats.copy(),
            "text_processing": {
                "max_seq_length": self.max_seq_length,
                "safe_max_length": self.safe_max_length,
                "overlap_ratio": self.overlap_ratio
            },
            "batch_config": {
                "default_batch_size": self.default_batch_size,
                "max_batch_size": self.max_batch_size,
                "min_batch_size": self.min_batch_size,
                "adaptive_batch_size": self.adaptive_batch_size
            },
            "system_info": {
                "memory_usage": f"{psutil.virtual_memory().percent:.1f}%",
                "available_memory_gb": f"{psutil.virtual_memory().available / (1024**3):.2f}GB",
                "cpu_count": mp.cpu_count()
            }
        }

    def optimize_batch_size(self, target_time_per_batch: float = 5.0):
        """根据目标时间优化批处理大小"""
        if self.batch_stats['total_batches'] < 5:
            logger.info("批处理统计数据不足，无法优化")
            return

        current_avg_time = self.batch_stats['avg_batch_time']
        current_optimal = self.batch_stats['optimal_batch_size']

        if current_avg_time > target_time_per_batch * 1.2:
            # 处理时间过长，减小批处理大小
            new_size = max(self.min_batch_size, int(current_optimal * 0.8))
            logger.info(f"批处理时间过长({current_avg_time:.2f}s)，建议减小批处理大小: {current_optimal} -> {new_size}")
        elif current_avg_time < target_time_per_batch * 0.5:
            # 处理时间很短，可以增大批处理大小
            new_size = min(self.max_batch_size, int(current_optimal * 1.2))
            logger.info(f"批处理时间较短({current_avg_time:.2f}s)，建议增大批处理大小: {current_optimal} -> {new_size}")
        else:
            logger.info(f"当前批处理大小({current_optimal})已接近最优")
            return

        self.batch_stats['optimal_batch_size'] = new_size

    def reset_performance_stats(self):
        """重置性能统计信息"""
        with self._lock:
            self.batch_stats = {
                'total_batches': 0,
                'total_texts': 0,
                'total_time': 0,
                'avg_batch_time': 0,
                'optimal_batch_size': self.default_batch_size
            }
            self.encoding_times = []
        logger.info("性能统计信息已重置")

    def test_batch_performance(self, test_texts: List[str] = None, batch_sizes: List[int] = None) -> Dict[str, Any]:
        """测试不同批处理大小的性能"""
        if test_texts is None:
            # 生成测试文本
            test_texts = [
                f"这是测试文本 {i}，用于测试批处理性能。" * (10 + i % 20)
                for i in range(100)
            ]

        if batch_sizes is None:
            batch_sizes = [8, 16, 32, 64]

        results = {}
        original_adaptive = self.adaptive_batch_size

        try:
            # 临时禁用自适应批处理
            self.adaptive_batch_size = False

            for batch_size in batch_sizes:
                if batch_size > len(test_texts):
                    continue

                logger.info(f"测试批处理大小: {batch_size}")
                start_time = time.time()

                # 临时设置批处理大小
                original_default = self.default_batch_size
                self.default_batch_size = batch_size

                # 执行批量编码
                embeddings = self.encode_texts_batch(test_texts[:50], show_progress=False)

                # 恢复原始设置
                self.default_batch_size = original_default

                total_time = time.time() - start_time
                success_rate = sum(1 for emb in embeddings if emb) / len(embeddings)

                results[batch_size] = {
                    "total_time": total_time,
                    "avg_time_per_text": total_time / len(embeddings),
                    "success_rate": success_rate,
                    "texts_per_second": len(embeddings) / total_time if total_time > 0 else 0
                }

                logger.info(f"批处理大小 {batch_size}: {total_time:.2f}s, {results[batch_size]['texts_per_second']:.1f} texts/s")

        finally:
            # 恢复自适应批处理设置
            self.adaptive_batch_size = original_adaptive

        # 找出最优批处理大小
        best_batch_size = max(results.keys(), key=lambda k: results[k]['texts_per_second'])
        logger.info(f"性能测试完成，最优批处理大小: {best_batch_size}")

        return {
            "results": results,
            "recommended_batch_size": best_batch_size,
            "test_info": {
                "num_test_texts": len(test_texts),
                "tested_batch_sizes": batch_sizes
            }
        }
